<script lang="ts">
	import { t } from '$lib/stores/i18n';

	import { enhance } from '$app/forms';
	import { Button, Modal, Label, Input, Alert, Select, Textarea } from 'flowbite-svelte';
	import {
		PhoneSolid,
		CogSolid,
		ExclamationCircleSolid,
		UserCircleSolid
	} from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

	// export let user: any;

	let signUpForm: HTMLFormElement;
	let signUpModalOpen = false;
	// let selectInstances: any = null;

	// State variables for handling messages
	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {};

	// Flag to track if form should be reset when modal closes
	let shouldResetOnClose = true;

	// Tab navigation state
	let activeTab = 0;

	// Dropdown options for new fields
	const countryOptions = [
		{ value: '', name: 'Select country' },
		{ value: 'TH', name: 'Thailand' },
		{ value: 'US', name: 'United States' },
		{ value: 'GB', name: 'United Kingdom' },
		{ value: 'AU', name: 'Australia' },
		{ value: 'CA', name: 'Canada' },
		{ value: 'SG', name: 'Singapore' },
		{ value: 'MY', name: 'Malaysia' },
		{ value: 'JP', name: 'Japan' },
		{ value: 'KR', name: 'South Korea' }
	];

	const languageOptions = [
		{ value: '', name: 'Select language' },
		{ value: 'en', name: 'English' },
		{ value: 'th', name: 'Thai' }
	];

	const timezoneOptions = [
		{ value: '', name: 'Select timezone' },
		{ value: 'Asia/Bangkok', name: 'Asia/Bangkok (UTC+7)' },
		{ value: 'America/New_York', name: 'America/New_York (UTC-5)' },
		{ value: 'Europe/London', name: 'Europe/London (UTC+0)' },
		{ value: 'Asia/Tokyo', name: 'Asia/Tokyo (UTC+9)' },
		{ value: 'Australia/Sydney', name: 'Australia/Sydney (UTC+10)' }
	];

	const dateFormatOptions = [
		{ value: '', name: 'Select format' },
		{ value: 'DD/MM/YYYY', name: 'DD/MM/YYYY' },
		{ value: 'MM/DD/YYYY', name: 'MM/DD/YYYY' },
		{ value: 'YYYY-MM-DD', name: 'YYYY-MM-DD' }
	];

	const themeOptions = [
		{ value: '', name: 'Select theme' },
		{ value: 'light', name: 'Light' },
		{ value: 'dark', name: 'Dark' },
		{ value: 'system', name: 'System' }
	];

	const notificationOptions = [
		{ value: '', name: 'Select notification preference' },
		{ value: 'all', name: 'All notifications' },
		{ value: 'important', name: 'Important only' },
		{ value: 'none', name: 'None' }
	];

	const relationshipOptions = [
		{ value: '', name: 'Select relationship' },
		{ value: 'spouse', name: 'Spouse' },
		{ value: 'parent', name: 'Parent' },
		{ value: 'child', name: 'Child' },
		{ value: 'sibling', name: 'Sibling' },
		{ value: 'friend', name: 'Friend' },
		{ value: 'colleague', name: 'Colleague' },
		{ value: 'other', name: 'Other' }
	];

	// Function to dismiss all error alerts when user starts typing
	function dismissAlerts() {
		showErrorMessage = false;
		errorMessage = '';
		fieldErrors = {};
	}

	// Function to reset form to initial state
	function resetForm() {
		formData = {
			// Basic Info (Required)
			username: '',
			password: '',
			confirm_password: '',
			name: '',
			email: '',
			employee_id: String(count + 1),
			first_name: '',
			last_name: '',
			department: '',
			role: '',
			is_active: true,
			is_staff: true,
			is_superuser: false,

			// Contact Info (Optional)
			personal_phone: '',
			work_phone: '',
			personal_email_alt: '',
			address: '',
			city: '',
			state_province: '',
			zip_postal_code: '',
			country: '',

			// Preferences (Optional)
			preferred_language: '',
			timezone: '',
			date_format: '',
			theme_preference: '',
			notification_preferences: '',

			// Emergency Contact (Optional)
			emergency_contact_name: '',
			emergency_relationship: '',
			emergency_contact_phone: '',
			emergency_contact_email: '',
			emergency_contact_address: ''
		};
		passwordFieldsEverTyped = false;
		activeTab = 0; // Reset to first tab
	}

	// Combined handler for password input (existing functionality + alert dismissal)
	function handlePasswordInput() {
		passwordFieldsEverTyped = true;
		dismissAlerts();
	}

	// Tab navigation functions
	function nextTab() {
		if (activeTab < 3) {
			activeTab++;
		}
	}

	function previousTab() {
		if (activeTab > 0) {
			activeTab--;
		}
	}

	function goToTab(tabIndex: number) {
		if (tabIndex >= 0 && tabIndex <= 3) {
			activeTab = tabIndex;
		}
	}

	// function openSignUpModal(user: any) {
	function openSignUpModal() {
		// selectInstances = { ...user };
		signUpModalOpen = true;
		// Reset messages when opening modal
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
		passwordFieldsEverTyped = false;
		// Reset the flag when opening modal
		shouldResetOnClose = true;
	}

	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	function handleSignUpSubmit(event: Event) {
		// Validate password matches confirm password
		// // TODO - Delete this
		// console.log(`handleSignUpSubmit's formData.password  - ${formData.password}`)
		// console.log(`handleSignUpSubmit's formData.confirm_password  - ${formData.confirm_password}`)

		// if (formData.password !== formData.confirm_password) {
		//     event.preventDefault();
		//     showErrorMessage = true;
		//     errorMessage = 'Passwords do not match';
		//     return false;
		// }

		// showSuccessMessage = false;
		// showErrorMessage = false;
		// successMessage = '';
		// errorMessage = '';

		// Reset messages
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};

		// // Check if passwords match
		// if (formData.password !== formData.confirm_password) {
		//     event.preventDefault();
		//     showErrorMessage = true;
		//     errorMessage = 'Passwords do not match';
		//     return ;
		// }

		return true;
	}

	// TODO - Delete this if the new one is work
	// $: enhanceOptions = {
	//     modalOpen: signUpModalOpen,
	//     setModalOpen: (value: boolean) => signUpModalOpen = value,
	//     setSuccessMessage: (value: boolean) => showSuccessMessage = value,
	//     setErrorMessage: (value: boolean) => showErrorMessage = value,
	//     setErrorText: (value: string) => errorMessage = value
	// };

	$: enhanceOptions = {
		modalOpen: signUpModalOpen,
		setModalOpen: (value: boolean) => {
			signUpModalOpen = value;
			// If modal is closing and we should reset form, do it now
			if (!value && shouldResetOnClose) {
				resetForm();
			}
		},
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => {
			successMessage = value;
			// Set flag to reset form when modal closes after successful submission
			shouldResetOnClose = true;
		},
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// New properties for enhanced success behavior
		useToastOnSuccess: true,
		closeModalOnSuccess: true
	};
	export let count: number;

	// Form data with initial values from user
	let formData = {
		// Basic Info (Required)
		username: '',
		password: '',
		confirm_password: '',
		name: '',
		email: '',
		// employee_id: "",
		employee_id: String(count + 1),
		first_name: '',
		last_name: '',
		department: '',
		role: '',
		is_active: true,
		is_staff: true,
		is_superuser: false,

		// Contact Info (Optional)
		personal_phone: '',
		work_phone: '',
		personal_email_alt: '',
		address: '',
		city: '',
		state_province: '',
		zip_postal_code: '',
		country: '',

		// Preferences (Optional)
		preferred_language: '',
		timezone: '',
		date_format: '',
		theme_preference: '',
		notification_preferences: '',

		// Emergency Contact (Optional)
		emergency_contact_name: '',
		emergency_relationship: '',
		emergency_contact_phone: '',
		emergency_contact_email: '',
		emergency_contact_address: ''
	};
	// TODO - Delete this
	// onst roles = ['Admin', 'Supervisor', 'Agent']; // Update based on your available roles

	// Password validation (similar to UserProfile)
	const specialChars = '!@#$%^&*';
	let passwordFieldsEverTyped = false;

	function checkPasswordRules(password: string) {
		return {
			length: password.length > 8,
			lowercase: /[a-z]/.test(password),
			uppercase: /[A-Z]/.test(password),
			special: new RegExp(`[${specialChars.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}]`).test(
				password
			),
			number: /[0-9]/.test(password)
		};
	}

	$: passwordRulesStatus = checkPasswordRules(formData.password);
	$: allPasswordRulesPassed = Object.values(passwordRulesStatus).every((value) => value === true);
	$: passwordsMatch =
		formData.password === formData.confirm_password && formData.password.length > 0;
</script>

<Button
	size="sm"
	class="bg-green-600 text-white hover:bg-green-700"
	on:click={() => openSignUpModal()}
>
	+ {t('new_account')}
</Button>

<Modal bind:open={signUpModalOpen} size="md" title="Sign-up User Information">
	<h3 slot="header">{t('create_account')}</h3>
	{#if showSuccessMessage}
		<Alert color="green" class="mb-4">
			{successMessage}
		</Alert>
	{/if}
	{#if showErrorMessage && errorMessage}
		<Alert color="red" class="mb-4">
			{errorMessage}
		</Alert>
	{/if}

	<!-- Custom Tab Navigation -->
	<div class="mb-6 flex border-b border-gray-200 sm:flex-row">
		<button
			type="button"
			class="flex items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors {activeTab ===
			0
				? 'border-blue-500 text-blue-600'
				: 'border-transparent text-gray-500 hover:text-gray-700'}"
			on:click={() => goToTab(0)}
		>
			<UserCircleSolid size="sm" />
			Basic Info
		</button>
		<button
			type="button"
			class="flex items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors {activeTab ===
			1
				? 'border-blue-500 text-blue-600'
				: 'border-transparent text-gray-500 hover:text-gray-700'}"
			on:click={() => goToTab(1)}
		>
			<PhoneSolid size="sm" />
			Contact
		</button>
		<button
			type="button"
			class="flex items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors {activeTab ===
			2
				? 'border-blue-500 text-blue-600'
				: 'border-transparent text-gray-500 hover:text-gray-700'}"
			on:click={() => goToTab(2)}
		>
			<CogSolid size="sm" />
			Preferences
		</button>
		<button
			type="button"
			class="flex items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors {activeTab ===
			3
				? 'border-blue-500 text-blue-600'
				: 'border-transparent text-gray-500 hover:text-gray-700'}"
			on:click={() => goToTab(3)}
		>
			<ExclamationCircleSolid size="sm" />
			Emergency
		</button>
	</div>

	<!-- Tab Content Indicators -->
	{#if activeTab === 0}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">Required</span
			>
			<span class="text-sm text-gray-600">All fields in this section are required</span>
		</div>
	{:else if activeTab === 1}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800"
				>Optional</span
			>
			<span class="text-sm text-gray-600">Contact information for better communication</span>
		</div>
	{:else if activeTab === 2}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800"
				>Optional</span
			>
			<span class="text-sm text-gray-600">Customize your experience</span>
		</div>
	{:else if activeTab === 3}
		<div class="mb-4 flex items-center gap-2">
			<span class="rounded bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800"
				>Optional</span
			>
			<span class="text-sm text-gray-600">Emergency contact information for safety</span>
		</div>
	{/if}

	<form
		bind:this={signUpForm}
		action="?/sign_up_user"
		method="POST"
		use:enhance={() => handleEnhance(enhanceOptions)}
		class="space-y-4 h-[650px] overflow-y-auto"
		on:submit={handleSignUpSubmit}
	>
		<!-- Basic Info Tab Content -->
		{#if activeTab === 0}
			<div>
				<Label for="employee_id" class="space-y-2">
					{t('employee_id')}
				</Label>
				<Input
					id="employee_id"
					name="employee_id"
					type="text"
					bind:value={formData.employee_id}
					required
					disabled
					readonly
				/>
			</div>

			<div>
				<Label for="name" class="space-y-2">
					{t('nickname')}<span class="text-red-600">*</span>
				</Label>
				<Input
					id="name"
					name="name"
					type="text"
					bind:value={formData.name}
					on:input={dismissAlerts}
					required
				/>
			</div>

			<!-- <div>
            <Label for="first_name" class="space-y-2">First name</Label>
            <Input
                id="first_name"
                name="first_name"
                type="text"
                bind:value={formData.first_name}
                required
            />
        </div>

        <div>
            <Label for="last_name" class="space-y-2">Last name</Label>
            <Input
                id="last_name"
                name="last_name"
                type="text"
                bind:value={formData.last_name}
                required
            />
        </div> -->

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="first_name" class="space-y-2">
						{t('first_name')}<span class="text-red-600">*</span>
					</Label>
					<Input
						id="first_name"
						name="first_name"
						type="text"
						bind:value={formData.first_name}
						on:input={dismissAlerts}
						required
					/>
					{#if fieldErrors.first_name}
						{#each fieldErrors.first_name as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>

				<div>
					<Label for="last_name" class="space-y-2">
						{t('last_name')}<span class="text-red-600">*</span>
					</Label>
					<Input
						id="last_name"
						name="last_name"
						type="text"
						bind:value={formData.last_name}
						on:input={dismissAlerts}
						required
					/>
					{#if fieldErrors.last_name}
						{#each fieldErrors.last_name as error}
							<Alert color="red" class="mt-1 px-3 py-2 text-sm">
								{error}
							</Alert>
						{/each}
					{/if}
				</div>
			</div>

			<div>
				<Label for="username" class="space-y-2">
					{t('username')}<span class="text-red-600">*</span>
				</Label>
				<Input
					id="username"
					name="username"
					type="text"
					bind:value={formData.username}
					on:input={dismissAlerts}
					required
				/>
				{#if fieldErrors.username}
					{#each fieldErrors.username as error}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							<!-- {error} -->
							{t('signup_error_duplicated_username')}
						</Alert>
					{/each}
				{/if}
			</div>

			

			<div>
				<Label for="password" class="space-y-2">
					{t('password')}<span class="text-red-600">*</span>
				</Label>
				<Input
					id="password"
					name="password"
					type="password"
					bind:value={formData.password}
					on:input={handlePasswordInput}
					required
				/>
				<div>
					<div class="mb-1 mt-2 text-xs font-normal text-gray-400">
						{t('password_validation_msg_1')}
					</div>
					<ul class="space-y-0 text-xs">
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.length
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_2')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.lowercase && passwordRulesStatus.uppercase
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_3')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.number
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_4')}</span
							>
						</li>
						<li class="flex items-center">
							<span
								class={passwordRulesStatus.special
									? 'text-green-600'
									: passwordFieldsEverTyped
										? 'text-red-600'
										: 'text-gray-400'}>{t('password_validation_msg_5')} ({specialChars})</span
							>
						</li>
					</ul>
				</div>
				{#if fieldErrors.password}
					{#each fieldErrors.password as error}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{error}
						</Alert>
					{/each}
				{/if}
			</div>

			<div>
				<Label for="confirm_password" class="space-y-2">
					{t('confirm_password')}<span class="text-red-600">*</span>
				</Label>
				<Input
					id="confirm_password"
					name="confirm_password"
					type="password"
					bind:value={formData.confirm_password}
					on:input={handlePasswordInput}
					required
				/>
				<div style="min-height:1em;" class="justify-left items-top mt-2 flex">
					{#if passwordFieldsEverTyped && !passwordsMatch && formData.confirm_password.length > 0}
						<span class="text-left text-xs text-red-600"
							>{t('password_validation_msg_do_not_match')}</span
						>
					{/if}
				</div>
				{#if fieldErrors.confirm_password}
					{#each fieldErrors.confirm_password as error}
						<Alert color="red" class="mt-1 px-3 py-2 text-sm">
							{error}
						</Alert>
					{/each}
				{/if}
			</div>

			<!-- <div>
            <Label for="department" class="space-y-2">Department</Label>
            <Input
                id="department"
                name="department"
                type="text"
                bind:value={formData.department}
            />
        </div>

        <div>
            <Label for="role" class="space-y-2">Role</Label>
            <Select 
                id="role" 
                name="role" 
                bind:value={formData.role}
                required
            >
                {#each roles as role}
                    <option value={role}>{role}</option>
                {/each}
            </Select>
        </div> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active} bind:value={formData.is_active}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_active">
            Enable Active Status
        </Checkbox>
        <input type="hidden" name="is_active" value={formData.is_active}> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_staff} bind:value={formData.is_staff}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_staff">
            Enable Staff
        </Checkbox>                 -->
			<!-- <input type="hidden" name="is_staff" value={formData.is_staff}> -->

			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_superuser} bind:value={formData.is_superuser}> -->
			<!-- <Checkbox class="flex items-center gap-2" bind:checked={formData.is_superuser">
            Enable Superuser
        </Checkbox>
        <input type="hidden" name="is_superuser" value={formData.is_superuser}> -->
		{/if}

		<!-- Contact Tab Content -->
		{#if activeTab === 1}
			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="personal_phone" class="space-y-2">Personal Phone</Label>
					<Input
						id="personal_phone"
						name="personal_phone"
						type="tel"
						bind:value={formData.personal_phone}
						on:input={dismissAlerts}
					/>
				</div>

				<div>
					<Label for="work_phone" class="space-y-2">Work Phone</Label>
					<Input
						id="work_phone"
						name="work_phone"
						type="tel"
						bind:value={formData.work_phone}
						on:input={dismissAlerts}
					/>
				</div>
			</div>

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="personal_email_alt" class="space-y-2">Personal Email (if different)</Label>
					<Input
					id="personal_email_alt"
					name="personal_email_alt"
					type="email"
					bind:value={formData.personal_email_alt}
					on:input={dismissAlerts}
					/>
				</div>
				
				<div>
					<Label for="work_email" class="space-y-2">
						Work Email
					</Label>
					<Input
					id="work_email"
					name="work_email"
					type="email"
					bind:value={formData.work_email}
					on:input={dismissAlerts}
					required
					/>
					{#if fieldErrors.email}
					{#each fieldErrors.email as error}
					<Alert color="red" class="mt-1 px-3 py-2 text-sm">
						<!-- {error} -->
						{t('signup_error_duplicated_email')}
					</Alert>
					{/each}
					{/if}
				</div>
			</div>

			<!-- <div>
				<Label for="address" class="space-y-2">Address</Label>
				<Textarea
					id="address"
					name="address"
					bind:value={formData.address}
					on:input={dismissAlerts}
					placeholder="Street address"
					rows={3}
				/>
			</div> -->

			<!-- <div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="city" class="space-y-2">City</Label>
					<Input
						id="city"
						name="city"
						type="text"
						bind:value={formData.city}
						on:input={dismissAlerts}
						placeholder="City"
					/>
				</div>

				<div>
					<Label for="state_province" class="space-y-2">State/Province</Label>
					<Input
						id="state_province"
						name="state_province"
						type="text"
						bind:value={formData.state_province}
						on:input={dismissAlerts}
						placeholder="State or Province"
					/>
				</div>
			</div> -->

			<!-- <div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="zip_postal_code" class="space-y-2">ZIP/Postal Code</Label>
					<Input
						id="zip_postal_code"
						name="zip_postal_code"
						type="text"
						bind:value={formData.zip_postal_code}
						on:input={dismissAlerts}
						placeholder="12345"
					/>
				</div>

				<div>
					<Label for="country" class="space-y-2">Country</Label>
					<Select
						id="country"
						name="country"
						bind:value={formData.country}
						items={countryOptions}
						on:change={dismissAlerts}
					/>
				</div>
			</div> -->
		{/if}

		<!-- Preferences Tab Content -->
		{#if activeTab === 2}
			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="preferred_language" class="space-y-2">Preferred Language</Label>
					<Select
						id="preferred_language"
						name="preferred_language"
						bind:value={formData.preferred_language}
						items={languageOptions}
						on:change={dismissAlerts}
					/>
				</div>

				<!-- <div>
					<Label for="timezone" class="space-y-2">Timezone</Label>
					<Select
						id="timezone"
						name="timezone"
						bind:value={formData.timezone}
						items={timezoneOptions}
						on:change={dismissAlerts}
					/>
				</div> -->
			</div>

			<!-- <div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="date_format" class="space-y-2">Date Format</Label>
					<Select
						id="date_format"
						name="date_format"
						bind:value={formData.date_format}
						items={dateFormatOptions}
						on:change={dismissAlerts}
					/>
				</div>

				<div>
					<Label for="theme_preference" class="space-y-2">Theme Preference</Label>
					<Select
						id="theme_preference"
						name="theme_preference"
						bind:value={formData.theme_preference}
						items={themeOptions}
						on:change={dismissAlerts}
					/>
				</div>
			</div> -->

			<!-- <div>
				<Label for="notification_preferences" class="space-y-2">Notification Preferences</Label>
				<Select
					id="notification_preferences"
					name="notification_preferences"
					bind:value={formData.notification_preferences}
					items={notificationOptions}
					on:change={dismissAlerts}
				/>
			</div> -->
		{/if}

		<!-- Emergency Tab Content -->
		{#if activeTab === 3}
			<div>
				<Label for="emergency_contact_name" class="space-y-2">Emergency Contact Name</Label>
				<Input
					id="emergency_contact_name"
					name="emergency_contact_name"
					type="text"
					bind:value={formData.emergency_contact_name}
					on:input={dismissAlerts}
				/>
			</div>

			<!-- <div>
				<Label for="emergency_relationship" class="space-y-2">Relationship</Label>
				<Select
					id="emergency_relationship"
					name="emergency_relationship"
					bind:value={formData.emergency_relationship}
					items={relationshipOptions}
					on:change={dismissAlerts}
				/>
			</div> -->

			<div class="grid grid-cols-2 gap-4">
				<div>
					<Label for="emergency_contact_phone" class="space-y-2">Emergency Contact Phone</Label>
					<Input
						id="emergency_contact_phone"
						name="emergency_contact_phone"
						type="tel"
						bind:value={formData.emergency_contact_phone}
						on:input={dismissAlerts}
					/>
				</div>

				<div>
					<Label for="emergency_contact_email" class="space-y-2">Emergency Contact Email</Label>
					<Input
						id="emergency_contact_email"
						name="emergency_contact_email"
						type="email"
						bind:value={formData.emergency_contact_email}
						on:input={dismissAlerts}
					/>
				</div>
			</div>

			<div>
				<Label for="emergency_contact_address" class="space-y-2">Emergency Contact Address</Label>
				<Textarea
					id="emergency_contact_address"
					name="emergency_contact_address"
					bind:value={formData.emergency_contact_address}
					on:input={dismissAlerts}
					rows={3}
				/>
			</div>
		{/if}

		<!-- Always include hidden fields -->
		<input type="hidden" name="is_active" value="true" />
		<input type="hidden" name="is_staff" value="true" />
		<input type="hidden" name="is_superuser" value="false" />
	</form>
	<svelte:fragment slot="footer">
		<div class="flex w-full justify-between">
			<div class="flex gap-2">
				<Button color="alternative" on:click={() => (signUpModalOpen = false)}>{t('cancel')}</Button
				>
				{#if activeTab > 0}
					<Button color="light" on:click={previousTab}>Previous</Button>
				{/if}
			</div>

			<div class="flex gap-2">
				{#if activeTab < 3}
					<Button color="blue" on:click={nextTab}>Next</Button>
				{:else}
					<Button
						type="submit"
						color="blue"
						disabled={!allPasswordRulesPassed || !passwordsMatch}
						on:click={() => signUpForm.requestSubmit()}
					>
						Create Account
					</Button>
				{/if}
			</div>
		</div>
	</svelte:fragment>
</Modal>
